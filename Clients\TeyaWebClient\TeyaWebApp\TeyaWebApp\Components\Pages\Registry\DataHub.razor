@page "/Registry/DataHub"
@using Microsoft.AspNetCore.Authorization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@using TeyaUIModels.Model
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<TeyaAIScribeResource.TeyaAIScribeResource> Localizer
@layout Admin
@attribute [Authorize]

<PageTitle>@(Localizer["DataHub"] ?? "DataHub")</PageTitle>

@if (IsLoading)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudProgressCircular Indeterminate="true" />
        <MudText>@(Localizer["LoadingDataHub"] ?? "Loading DataHub...")</MudText>
    </MudContainer>
}
else if (HasError)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudAlert Severity="Severity.Error">
            <MudText>@ErrorMessage</MudText>
            <MudButton Color="Color.Primary" OnClick="RefreshPage" Class="mt-2">
                @(Localizer["RefreshPage"] ?? "Refresh Page")
            </MudButton>
        </MudAlert>
    </MudContainer>
}
else
{
<div class="datahub-container">
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-4">
        <!-- Header -->
        <MudPaper Class="pa-4 mb-4" Elevation="2">
            <MudText Typo="Typo.h4" Class="mb-2">
                <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
                @(Localizer["DataHub"] ?? "DataHub")
            </MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary">
                @(Localizer["PatientRegistryManagement"] ?? "Patient Registry Management")
            </MudText>
        </MudPaper>

        <!-- Filter Section -->
        <MudPaper Class="pa-4 mb-4" Elevation="1">
            <MudText Typo="Typo.h6" Class="mb-3">@(Localizer["PatientSearchFilters"] ?? "Patient Search Filters")</MudText>

            <MudGrid>
                <!-- Age Range -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.AgeFrom"
                                  Label="@(Localizer["AgeFrom"] ?? "Age From")"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  T="int?" />
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.AgeTo"
                                  Label="@(Localizer["AgeTo"] ?? "Age To")"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  T="int?" />
                </MudItem>

                <!-- Sex -->
                <MudItem xs="12" sm="6" md="3">
                    <MudSelect @bind-Value="FilterModel.Sex"
                               Label="@(Localizer["Sex"] ?? "Sex")"
                               Variant="Variant.Outlined"
                               Margin="Margin.Dense"
                               T="string">
                        <MudSelectItem Value="@("")">@(Localizer["All"] ?? "All")</MudSelectItem>
                        <MudSelectItem Value="@("Male")">@(Localizer["Male"] ?? "Male")</MudSelectItem>
                        <MudSelectItem Value="@("Female")">@(Localizer["Female"] ?? "Female")</MudSelectItem>
                        <MudSelectItem Value="@("Other")">@(Localizer["Other"] ?? "Other")</MudSelectItem>
                    </MudSelect>
                </MudItem>

                <!-- Patient Name -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.PatientName"
                                  Label="@(Localizer["PatientName"] ?? "Patient Name")"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudItem>

                <!-- Phone Number -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.PhoneNumber"
                                  Label="@(Localizer["PhoneNumber"] ?? "Phone Number")"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudItem>

                <!-- Insurance -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.Insurance"
                                  Label="@(Localizer["Insurance"] ?? "Insurance")"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudItem>
            </MudGrid>

            <!-- Action Buttons -->
            <MudGrid Class="mt-4">
                <MudItem xs="12">
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Search"
                               OnClick="SearchPatients"
                               Disabled="IsLoading"
                               Class="mr-2">
                        @if (IsLoading)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                        }
                        @(Localizer["SearchPatients"] ?? "Search Patients")
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               StartIcon="@Icons.Material.Filled.Clear"
                               OnClick="ClearFilters"
                               Disabled="IsLoading"
                               Class="mr-2">
                        @(Localizer["ClearFilters"] ?? "Clear Filters")
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Warning"
                               StartIcon="@Icons.Material.Filled.Download"
                               OnClick="ExportData"
                               Disabled="@(!HasResults || IsLoading)">
                        @(Localizer["ExportData"] ?? "Export Data")
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudPaper>

        <!-- Results Section -->
        @if (SearchPerformed)
        {
            @if (HasResults)
            {
                <MudPaper Class="pa-4" Elevation="1">
                    <MudText Typo="Typo.h6" Class="mb-3">
                        @(Localizer["SearchResults"] ?? "Search Results") (@TotalPatients @(Localizer["Patients"] ?? "Patients"))
                    </MudText>

                    <MudTable Items="@PatientResults"
                              Hover="true"
                              Striped="true"
                              Dense="true"
                              FixedHeader="true"
                              Height="500px"
                              Class="datahub-table">
                        <HeaderContent>
                            <MudTh><MudTableSortLabel SortBy="new Func<PatientRegistryModel, object>(x=>x.FullName)">@(Localizer["PatientName"] ?? "Patient Name")</MudTableSortLabel></MudTh>
                            <MudTh><MudTableSortLabel SortBy="new Func<PatientRegistryModel, object>(x=>x.DateOfBirth)">@(Localizer["DateOfBirth"] ?? "Date of Birth")</MudTableSortLabel></MudTh>
                            <MudTh><MudTableSortLabel SortBy="new Func<PatientRegistryModel, object>(x=>x.Sex)">@(Localizer["Sex"] ?? "Sex")</MudTableSortLabel></MudTh>
                            <MudTh><MudTableSortLabel SortBy="new Func<PatientRegistryModel, object>(x=>x.Age)">@(Localizer["Age"] ?? "Age")</MudTableSortLabel></MudTh>
                            <MudTh>@(Localizer["PhoneNumber"] ?? "Phone Number")</MudTh>
                            <MudTh>@(Localizer["Email"] ?? "Email")</MudTh>
                            <MudTh>@(Localizer["PCP"] ?? "PCP")</MudTh>
                            <MudTh>@(Localizer["Insurance"] ?? "Insurance")</MudTh>
                            <MudTh>@(Localizer["Status"] ?? "Status")</MudTh>
                            <MudTh>@(Localizer["Actions"] ?? "Actions")</MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="Patient Name">
                                <div class="d-flex align-center">
                                    <MudAvatar Color="Color.Primary" Size="Size.Small" Class="mr-2">
                                        @context.FullName.Substring(0, Math.Min(2, context.FullName.Length)).ToUpper()
                                    </MudAvatar>
                                    <div>
                                        <MudText Typo="Typo.body2" Class="font-weight-medium">@context.FullName</MudText>
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">ID: @context.Id.ToString().Substring(0, 8)</MudText>
                                    </div>
                                </div>
                            </MudTd>
                            <MudTd DataLabel="Date of Birth">@context.DateOfBirth?.ToString("MM/dd/yyyy")</MudTd>
                            <MudTd DataLabel="Sex">@context.Sex</MudTd>
                            <MudTd DataLabel="Age">@context.Age</MudTd>
                            <MudTd DataLabel="Phone Number">@context.PhoneNumber</MudTd>
                            <MudTd DataLabel="Email">
                                <MudText Typo="Typo.body2" Class="text-truncate" Style="max-width: 150px;">@context.Email</MudText>
                            </MudTd>
                            <MudTd DataLabel="PCP">@context.PCP</MudTd>
                            <MudTd DataLabel="Insurance">@context.Insurance</MudTd>
                            <MudTd DataLabel="Status">
                                <MudChip Color="@(context.IsActive ? Color.Success : Color.Default)"
                                         Size="Size.Small"
                                         Variant="Variant.Filled">
                                    @(context.IsActive ? (Localizer["Active"] ?? "Active") : (Localizer["Inactive"] ?? "Inactive"))
                                </MudChip>
                            </MudTd>
                            <MudTd DataLabel="Actions">
                                <MudTooltip Text="@(Localizer["ViewPatient"] ?? "View Patient")">
                                    <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                   Color="Color.Primary"
                                                   Size="Size.Small"
                                                   OnClick="@(() => ViewPatient(context.Id))"
                                                   Variant="Variant.Text" />
                                </MudTooltip>
                                <MudTooltip Text="@(Localizer["EditPatient"] ?? "Edit Patient")">
                                    <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                   Color="Color.Secondary"
                                                   Size="Size.Small"
                                                   OnClick="@(() => EditPatient(context.Id))"
                                                   Variant="Variant.Text" />
                                </MudTooltip>
                            </MudTd>
                        </RowTemplate>
                    </MudTable>
                </MudPaper>
            }
            else
            {
                <MudPaper Class="pa-4" Elevation="1">
                    <MudAlert Severity="Severity.Info">
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.SearchOff" Class="mr-2" />
                            <div>
                                <MudText Typo="Typo.body1">@(Localizer["NoResultsFound"] ?? "No patients found matching your search criteria.")</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">@(Localizer["TryDifferentFilters"] ?? "Try adjusting your search filters or clearing them to see all patients.")</MudText>
                            </div>
                        </div>
                    </MudAlert>
                </MudPaper>
            }
        }
        else
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudAlert Severity="Severity.Info">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
                        <div>
                            <MudText Typo="Typo.h6">@(Localizer["WelcomeToDataHub"] ?? "Welcome to DataHub - Patient Registry Management")</MudText>
                            <MudText Typo="Typo.body2" Class="mt-2">@(Localizer["DataHubInstructions"] ?? "Use the filters above to search for patients in your organization.")</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Secondary">@(Localizer["DataHubFeatures"] ?? "You can filter by age, sex, name, phone number, insurance, and more.")</MudText>
                        </div>
                    </div>
                </MudAlert>
            </MudPaper>
        }

    </MudContainer>
</div>
}
