@page "/Registry/DataHub"
@using Microsoft.AspNetCore.Authorization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@using TeyaUIModels.Model
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<TeyaAIScribeResource.TeyaAIScribeResource> Localizer
@layout Admin
@attribute [Authorize]

<PageTitle>@(Localizer["DataHub"] ?? "DataHub")</PageTitle>

@if (IsLoading)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudProgressCircular Indeterminate="true" />
        <MudText>@(Localizer["LoadingDataHub"] ?? "Loading DataHub...")</MudText>
    </MudContainer>
}
else if (HasError)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudAlert Severity="Severity.Error">
            <MudText>@ErrorMessage</MudText>
            <MudButton Color="Color.Primary" OnClick="RefreshPage" Class="mt-2">
                @(Localizer["RefreshPage"] ?? "Refresh Page")
            </MudButton>
        </MudAlert>
    </MudContainer>
}
else
{
<div class="datahub-container">
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-4">
        <!-- Header -->
        <MudPaper Class="pa-4 mb-4" Elevation="2">
            <MudText Typo="Typo.h4" Class="mb-2">
                <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
                @(Localizer["DataHub"] ?? "DataHub")
            </MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary">
                @(Localizer["PatientRegistryManagement"] ?? "Patient Registry Management")
            </MudText>
        </MudPaper>

        <!-- Registry Lock Status -->
        @if (IsRegistryLocked && !IsCurrentUserLockHolder)
        {
            <MudAlert Severity="Severity.Warning" Class="mb-4">
                <MudText>@(Localizer["RegistryLocked"] ?? "Registry is currently being used by another user. Please try again later.")</MudText>
                <MudButton Color="Color.Primary" OnClick="RequestLockRelease" Class="mt-2">
                    @(Localizer["RequestRelease"] ?? "Request Release")
                </MudButton>
            </MudAlert>
        }
        else
        {
            <!-- Registry Query Buttons -->
            <MudPaper Class="pa-4 mb-4" Elevation="1">
                <MudGrid>
                    <MudItem xs="12" md="8">
                        <MudButtonGroup Variant="Variant.Filled" Color="Color.Primary">
                            <MudButton StartIcon="@Icons.Material.Filled.PlayArrow" OnClick="RunNewQuery" Disabled="IsProcessing">
                                @(Localizer["RunNew"] ?? "Run New")
                            </MudButton>
                            <MudButton StartIcon="@Icons.Material.Filled.FilterList" OnClick="RunSubsetQuery" Disabled="IsProcessing || !HasPreviousResults">
                                @(Localizer["RunSubset"] ?? "Run Subset")
                            </MudButton>
                            <MudButton StartIcon="@Icons.Material.Filled.FilterListOff" OnClick="RunSubsetNotQuery" Disabled="IsProcessing || !HasPreviousResults">
                                @(Localizer["RunSubsetNot"] ?? "Run Subset (NOT)")
                            </MudButton>
                            <MudButton StartIcon="@Icons.Material.Filled.Save" OnClick="SaveQueries" Disabled="IsProcessing || !HasResults">
                                @(Localizer["SaveQueries"] ?? "Save Queries")
                            </MudButton>
                        </MudButtonGroup>
                    </MudItem>
                    <MudItem xs="12" md="4" Class="d-flex justify-end">
                        <MudButton StartIcon="@Icons.Material.Filled.LockOpen" OnClick="ReleaseLock" Color="Color.Secondary" Variant="Variant.Outlined">
                            @(Localizer["ReleaseLock"] ?? "Release Lock")
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </MudPaper>

            <!-- Tab Navigation -->
            <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6" @bind-ActivePanelIndex="ActiveTabIndex">
                <!-- Demographics Tab -->
                <MudTabPanel Text="@(Localizer["Demographics"] ?? "Demographics")" Icon="@Icons.Material.Filled.People">
                    @await RenderDemographicsTab()
                </MudTabPanel>

                <!-- Vitals Tab -->
                <MudTabPanel Text="@(Localizer["Vitals"] ?? "Vitals")" Icon="@Icons.Material.Filled.MonitorHeart">
                    @await RenderVitalsTab()
                </MudTabPanel>

                <!-- Labs/DI/Procedure Tab -->
                <MudTabPanel Text="@(Localizer["LabsDIProcedure"] ?? "Labs/DI/Procedure")" Icon="@Icons.Material.Filled.Science">
                    @await RenderLabsTab()
                </MudTabPanel>

                <!-- ICD Tab -->
                <MudTabPanel Text="@(Localizer["ICD"] ?? "ICD")" Icon="@Icons.Material.Filled.LocalHospital">
                    @await RenderICDTab()
                </MudTabPanel>

                <!-- CPT Tab -->
                <MudTabPanel Text="@(Localizer["CPT"] ?? "CPT")" Icon="@Icons.Material.Filled.Receipt">
                    @await RenderCPTTab()
                </MudTabPanel>

                <!-- Rx Tab -->
                <MudTabPanel Text="@(Localizer["Rx"] ?? "Rx")" Icon="@Icons.Material.Filled.Medication">
                    @await RenderRxTab()
                </MudTabPanel>

                <!-- Immunization Tab -->
                <MudTabPanel Text="@(Localizer["Immunization"] ?? "Imm/T.Inj")" Icon="@Icons.Material.Filled.Vaccines">
                    @await RenderImmunizationTab()
                </MudTabPanel>

                <!-- Encounters Tab -->
                <MudTabPanel Text="@(Localizer["Encounters"] ?? "Encounters")" Icon="@Icons.Material.Filled.Event">
                    @await RenderEncountersTab()
                </MudTabPanel>

                <!-- Allergies Tab -->
                <MudTabPanel Text="@(Localizer["Allergies"] ?? "Allergies")" Icon="@Icons.Material.Filled.Warning">
                    @await RenderAllergiesTab()
                </MudTabPanel>

                <!-- Medical History Tab -->
                <MudTabPanel Text="@(Localizer["MedicalHistory"] ?? "Medical History")" Icon="@Icons.Material.Filled.History">
                    @await RenderMedicalHistoryTab()
                </MudTabPanel>

                <!-- Structured Data Tab -->
                <MudTabPanel Text="@(Localizer["StructuredData"] ?? "Structured Data")" Icon="@Icons.Material.Filled.DataObject">
                    @await RenderStructuredDataTab()
                </MudTabPanel>

                <!-- Referrals Tab -->
                <MudTabPanel Text="@(Localizer["Referrals"] ?? "Referrals")" Icon="@Icons.Material.Filled.PersonSearch">
                    @await RenderReferralsTab()
                </MudTabPanel>

                <!-- Reports Tab -->
                <MudTabPanel Text="@(Localizer["Reports"] ?? "Reports")" Icon="@Icons.Material.Filled.Assessment">
                    @await RenderReportsTab()
                </MudTabPanel>

                <!-- Saved Reports Tab -->
                <MudTabPanel Text="@(Localizer["SavedReports"] ?? "Saved Reports")" Icon="@Icons.Material.Filled.BookmarkBorder">
                    @await RenderSavedReportsTab()
                </MudTabPanel>
            </MudTabs>
        }

        <!-- Results Section -->
        @if (HasResults)
        {
            <!-- Patient Actions Bar -->
            <MudPaper Class="pa-4 mb-4" Elevation="1">
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudText Typo="Typo.h6" Class="mb-2">@(Localizer["PatientActions"] ?? "Patient Actions")</MudText>
                        <MudButtonGroup Variant="Variant.Outlined" Size="Size.Small">
                            <MudButton StartIcon="@Icons.Material.Filled.PersonRemove" OnClick="ExcludeFromSearch" Disabled="!HasSelectedPatients">
                                @(Localizer["ExcludeFromSearch"] ?? "Exclude from Search")
                            </MudButton>
                            <MudButton StartIcon="@Icons.Material.Filled.PersonOff" OnClick="InactivatePatients" Disabled="!HasSelectedPatients">
                                @(Localizer["InactivatePatients"] ?? "Inactivate Patients")
                            </MudButton>
                            <MudButton StartIcon="@Icons.Material.Filled.Person" OnClick="ActivatePatients" Disabled="!HasSelectedPatients">
                                @(Localizer["ActivatePatients"] ?? "Activate Patients")
                            </MudButton>
                            <MudButton StartIcon="@Icons.Material.Filled.Visibility" OnClick="ShowExcludedPatients">
                                @(Localizer["ShowExcludedPatients"] ?? "Show Excluded Patients")
                            </MudButton>
                        </MudButtonGroup>
                    </MudItem>
                    <MudItem xs="12" md="6" Class="d-flex justify-end align-center">
                        <MudText Typo="Typo.body2" Class="mr-4">
                            @(Localizer["ResultsCount"] ?? "Results: {0}", TotalPatients)
                        </MudText>
                        <MudButtonGroup Variant="Variant.Filled" Size="Size.Small">
                            <MudButton StartIcon="@Icons.Material.Filled.ContentCopy" OnClick="CopyResults">
                                @(Localizer["Copy"] ?? "Copy")
                            </MudButton>
                            <MudButton StartIcon="@Icons.Material.Filled.Download" OnClick="ExportResults">
                                @(Localizer["Export"] ?? "Export")
                            </MudButton>
                            <MudButton StartIcon="@Icons.Material.Filled.Analytics" OnClick="AnalyzeData">
                                @(Localizer["AnalyzeData"] ?? "Analyze Data")
                            </MudButton>
                        </MudButtonGroup>
                    </MudItem>
                </MudGrid>
            </MudPaper>

            <!-- Patient Results Table -->
            <MudPaper Class="pa-4" Elevation="1">
                <MudText Typo="Typo.h6" Class="mb-3">
                    @(Localizer["SearchResults"] ?? "Search Results") (@TotalPatients @(Localizer["Patients"] ?? "Patients"))
                </MudText>

                <MudTable Items="@PatientResults"
                          MultiSelection="true"
                          @bind-SelectedItems="SelectedPatients"
                          Hover="true"
                          Striped="true"
                          Dense="true"
                          FixedHeader="true"
                          Height="500px"
                          Class="datahub-table">
                    <HeaderContent>
                        <MudTh><MudTableSortLabel SortBy="new Func<PatientRegistryModel, object>(x=>x.FullName)">@(Localizer["PatientName"] ?? "Patient Name")</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<PatientRegistryModel, object>(x=>x.DateOfBirth)">@(Localizer["DateOfBirth"] ?? "Date of Birth")</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<PatientRegistryModel, object>(x=>x.Sex)">@(Localizer["Sex"] ?? "Sex")</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<PatientRegistryModel, object>(x=>x.Age)">@(Localizer["Age"] ?? "Age")</MudTableSortLabel></MudTh>
                        <MudTh>@(Localizer["PhoneNumber"] ?? "Phone Number")</MudTh>
                        <MudTh>@(Localizer["Email"] ?? "Email")</MudTh>
                        <MudTh>@(Localizer["PCP"] ?? "PCP")</MudTh>
                        <MudTh>@(Localizer["Insurance"] ?? "Insurance")</MudTh>
                        <MudTh>@(Localizer["Status"] ?? "Status")</MudTh>
                        <MudTh>@(Localizer["Actions"] ?? "Actions")</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Patient Name">
                            <div class="d-flex align-center">
                                <MudAvatar Color="Color.Primary" Size="Size.Small" Class="mr-2">
                                    @context.FullName.Substring(0, Math.Min(2, context.FullName.Length)).ToUpper()
                                </MudAvatar>
                                <div>
                                    <MudText Typo="Typo.body2" Class="font-weight-medium">@context.FullName</MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">ID: @context.Id.ToString().Substring(0, 8)</MudText>
                                </div>
                            </div>
                        </MudTd>
                        <MudTd DataLabel="Date of Birth">@context.DateOfBirth?.ToString("MM/dd/yyyy")</MudTd>
                        <MudTd DataLabel="Sex">@context.Sex</MudTd>
                        <MudTd DataLabel="Age">@context.Age</MudTd>
                        <MudTd DataLabel="Phone Number">@context.PhoneNumber</MudTd>
                        <MudTd DataLabel="Email">
                            <MudText Typo="Typo.body2" Class="text-truncate" Style="max-width: 150px;">@context.Email</MudText>
                        </MudTd>
                        <MudTd DataLabel="PCP">@context.PCP</MudTd>
                        <MudTd DataLabel="Insurance">@context.Insurance</MudTd>
                        <MudTd DataLabel="Status">
                            <MudChip Color="@(context.IsActive ? Color.Success : Color.Default)"
                                     Size="Size.Small"
                                     Variant="Variant.Filled">
                                @(context.IsActive ? (Localizer["Active"] ?? "Active") : (Localizer["Inactive"] ?? "Inactive"))
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="Actions">
                            <MudMenu Icon="@Icons.Material.Filled.MoreVert" Size="Size.Small" Dense="true">
                                <MudMenuItem Icon="@Icons.Material.Filled.Person" OnClick="@(() => LaunchPatientHub(context.Id))">
                                    @(Localizer["PatientHub"] ?? "Patient Hub")
                                </MudMenuItem>
                                <MudMenuItem Icon="@Icons.Material.Filled.CalendarToday" OnClick="@(() => NewAppointment(context.Id))">
                                    @(Localizer["NewAppointment"] ?? "New Appointment")
                                </MudMenuItem>
                                <MudMenuItem Icon="@Icons.Material.Filled.Assignment" OnClick="@(() => OpenFlowsheet(context.Id))">
                                    @(Localizer["Flowsheet"] ?? "Flowsheet")
                                </MudMenuItem>
                                <MudMenuItem Icon="@Icons.Material.Filled.Message" OnClick="@(() => SendMessage(context.Id))">
                                    @(Localizer["Messenger"] ?? "Messenger")
                                </MudMenuItem>
                                <MudDivider />
                                <MudMenuItem Icon="@Icons.Material.Filled.Visibility" OnClick="@(() => ViewPatient(context.Id))">
                                    @(Localizer["ViewPatient"] ?? "View Patient")
                                </MudMenuItem>
                                <MudMenuItem Icon="@Icons.Material.Filled.Edit" OnClick="@(() => EditPatient(context.Id))">
                                    @(Localizer["EditPatient"] ?? "Edit Patient")
                                </MudMenuItem>
                            </MudMenu>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            </MudPaper>
        }
        else if (SearchPerformed)
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudAlert Severity="Severity.Info">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.SearchOff" Class="mr-2" />
                        <div>
                            <MudText Typo="Typo.body1">@(Localizer["NoResultsFound"] ?? "No patients found matching your search criteria.")</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Secondary">@(Localizer["TryDifferentFilters"] ?? "Try adjusting your search filters or clearing them to see all patients.")</MudText>
                        </div>
                    </div>
                </MudAlert>
            </MudPaper>
        }
        else
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudAlert Severity="Severity.Info">
                    <div class="d-flex align-center">
                        <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
                        <div>
                            <MudText Typo="Typo.h6">@(Localizer["WelcomeToDataHub"] ?? "Welcome to DataHub - Patient Registry Management")</MudText>
                            <MudText Typo="Typo.body2" Class="mt-2">@(Localizer["DataHubInstructions"] ?? "Use the tabs above to configure your search criteria, then click 'Run New' to search for patients.")</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Secondary">@(Localizer["DataHubFeatures"] ?? "You can search by demographics, vitals, labs, medications, and much more using the comprehensive tab system.")</MudText>
                        </div>
                    </div>
                </MudAlert>
            </MudPaper>
        }

    </MudContainer>
</div>
}
