﻿@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IDialogService DialogService
@inject NavigationManager NavigationManager
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@implements IDisposable

<head>
    <link href="../app.css" rel="stylesheet" />
</head>

<MudPaper Width="250px" Class="py-3" Elevation="0">
    <MudNavMenu>
        @if (!ShouldShowProductSwitcher() && (HasEHRProductAccess() || HasBillingProductAccess()))
        {
            <div class="px-3 py-2">
                <MudChip T="string"
                         Icon="@GetProductIcon(GetSelectedProduct())"
                         Size="Size.Small"
                         Color="Color.Primary"
                         Variant="Variant.Text">
                    @GetProductDisplayName(GetSelectedProduct())
                </MudChip>
            </div>
            <MudDivider Class="mb-3" />
        }

        @if (IsEHRSelected() && HasEHRProductAccess())
        {
            @if (IsPageAccessible(Menu))
            {
                <MudNavLink Class="mud-navlink-style" Href="/Menu" Icon="@Icons.Material.Filled.MenuBook">
                    @Localizer["Menu"]
                </MudNavLink>
            }

            @if (IsPageAccessible(Chart))
            {
                <MudNavLink Class="mud-navlink-style" Href="/Chart" Icon="@Icons.Material.Filled.Assignment">
                    @Localizer["Chart"]
                </MudNavLink>
            }

            @if (IsPageAccessible(Appointments))
            {
                <MudNavLink Class="mud-navlink-style" Href="/Appointments" Icon="@Icons.Material.Filled.Event">
                    @Localizer["Appointments"]
                </MudNavLink>
            }

            @if (IsPageAccessible(OrderSets))
            {
                <MudNavLink Class="mud-navlink-style" Href="/OrderSets" Icon="@Icons.Material.Filled.ListAlt">
                    @Localizer["OrderSets"]
                </MudNavLink>
            }

            @if (IsPageAccessible(Practice))
            {
                <MudNavGroup Class="mud-navgroup-style" Title="@Localizer["Practice"]" Expanded="true" Icon="@Icons.Material.Filled.Sports">
                    <MudNavLink Class="mud-navlink-style" Href="/Practice" Icon="@Icons.Material.Filled.Sports">
                        @Localizer["Tasks"]
                    </MudNavLink>
                    <MudNavLink Class="mud-navlink-style" Href="/OfficeVisit" Icon="@Icons.Material.Filled.People">
                        @Localizer["OfficeVisit"]
                    </MudNavLink>
                </MudNavGroup>
            }

            @if (ShouldShowEHRSettings())
            {
                <MudNavGroup Class="mud-navgroup-style" Title="@Localizer["Settings"]" Expanded="true" Icon="@Icons.Material.Filled.Settings">
                    @if (IsPageAccessible(Providers))
                    {
                        <MudNavLink Class="mud-navlink-style" Href="/Providers" Icon="@Icons.Material.Filled.People">
                            @Localizer["Providers"]
                        </MudNavLink>
                    }

                    @if (IsPageAccessible(Patients))
                    {
                        <MudNavLink Class="mud-navlink-style" Href="/Patients" Icon="@Icons.Material.Filled.People">
                            @Localizer["Patient"]
                        </MudNavLink>
                    }

                    @if (IsPageAccessible(Registry))
                    {
                        <MudNavGroup Class="mud-navgroup-style" Title="@Localizer["Registry"]" Expanded="false" Icon="@Icons.Material.Filled.AppRegistration">
                            <MudNavLink Class="mud-navlink-style" Href="/Registry/DataHub" Icon="@Icons.Material.Filled.Dashboard">
                                @Localizer["DataHub"]
                            </MudNavLink>
                        </MudNavGroup>
                    }

                    @if (ShouldRenderLicenseLink)
                    {
                        @if (IsPageAccessible(LicenseActivation))
                        {
                            <MudNavGroup Class="mud-navgroup-style" Title="@Localizer["LicenseActivation"]" Expanded="false" Icon="@Icons.Material.Filled.Verified">
                                @if (IsPageAccessible(License) && IsPageAccessible(LicenseActivation))
                                {
                                    <MudNavLink Class="mud-navlink-style" Href="/License" Icon="@Icons.Material.Filled.Approval">
                                        @Localizer["License"]
                                    </MudNavLink>
                                    <MudNavLink Class="mud-navlink-style" Href="/ProductFeatureSettings" Icon="@Icons.Material.Filled.ProductionQuantityLimits">
                                        @Localizer["ProductFeatures"]
                                    </MudNavLink>
                                }
                            </MudNavGroup>
                        }
                        @if (IsPageAccessible(Security))
                        {
                            <MudNavLink Class="mud-navlink-style" Href="/Security" Icon="@Icons.Material.Filled.VerifiedUser">
                                @Localizer["Security"]
                            </MudNavLink>
                        }
                    }

                    @if (IsPageAccessible(UserManagement))
                    {
                        <MudNavLink Class="mud-navlink-style" Href="/UserManagement" Icon="@Icons.Material.Filled.VerifiedUser">
                            @Localizer["UserManagement"]
                        </MudNavLink>
                    }

                    @if (IsPageAccessible(Templates))
                    {
                        <MudNavLink Class="mud-navlink-style" Href="/Templates" Icon="@Icons.Material.Filled.DocumentScanner">
                            @Localizer["Templates"]
                        </MudNavLink>
                    }

                    @if (IsPageAccessible(Config))
                    {
                        <MudNavLink Class="mud-navlink-style" Href="/Config" Icon="@Icons.Material.Filled.AdminPanelSettings">
                            @Localizer["Config"]
                        </MudNavLink>
                    }

                    @if (IsPageAccessible(PlanBilling))
                    {
                        <MudNavLink Class="mud-navlink-style" Href="/PlanBilling" Icon="@Icons.Material.Filled.Money">
                            @Localizer["PlanBilling"]
                        </MudNavLink>
                    }
                </MudNavGroup>
            }
        }
        else if (IsBillingSelected() && HasBillingProductAccess())
        {
            @if (IsPageAccessible(ClaimsLookup))
            {
                <MudNavLink Class="mud-navlink-style" Href="/ClaimsLookup" Icon="@Icons.Material.Filled.Search">
                    @Localizer["ClaimsLookup"]
                </MudNavLink>
            }

            @if (IsPageAccessible(Encounters))
            {
                <MudNavLink Class="mud-navlink-style" Href="/Encounters" Icon="@Icons.Material.Filled.Assignment">
                    @Localizer["Encounters"]
                </MudNavLink>
            }
        }

        @if (!_isLoading && _showNoProductMessage && !HasEHRProductAccess() && !HasBillingProductAccess())
        {
            <MudAlert Severity="Severity.Warning" Class="ma-2">
                <AlertContent>
                    <MudText Typo="Typo.body2">
                        @Localizer["NoProductsAvailable"]
                    </MudText>
                    <MudText Typo="Typo.caption">
                        @Localizer["ContactAdministrator"]
                    </MudText>
                </AlertContent>
            </MudAlert>
        }
    </MudNavMenu>
</MudPaper>