using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaAIScribeResource;

namespace TeyaWebApp.Components.Pages.Registry
{
    public partial class DataHub : ComponentBase
    {
        [Inject] private ISnackbar? Snackbar { get; set; }
        [Inject] private ILogger<DataHub>? Logger { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; } = default!;
        [Inject] private IStringLocalizer<TeyaAIScribeResource.TeyaAIScribeResource>? Localizer { get; set; }
        [Inject] private IPatientService? PatientService { get; set; }
        [Inject] private IMemberService? MemberService { get; set; }

        // Filter properties using proper model
        private PatientSearchCriteria SearchCriteria = new();
        private DataHubFilterModel FilterModel = new();

        // Results properties
        private List<PatientRegistryModel> PatientResults = new();
        private bool IsLoading = true;
        private bool HasError = false;
        private string ErrorMessage = string.Empty;
        private bool SearchPerformed = false;
        private bool HasResults => PatientResults?.Any() == true;
        private int TotalPatients => PatientResults?.Count ?? 0;

        // Organization context
        private Guid CurrentOrganizationId = Guid.Empty;

        private void RefreshPage()
        {
            NavigationManager.Refresh(true);
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                IsLoading = true;
                HasError = false;
                StateHasChanged();

                // Initialize organization context
                // In a real implementation, this would come from user context/session
                CurrentOrganizationId = Guid.NewGuid(); // Placeholder - should come from authenticated user context

                // Initialize search criteria
                SearchCriteria.OrganizationId = CurrentOrganizationId;

                IsLoading = false;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error initializing DataHub: {Message}", ex.Message);
                HasError = true;
                ErrorMessage = Localizer?["DataHubInitializationError"] ?? "Error initializing DataHub";
                IsLoading = false;
                StateHasChanged();
            }
        }

        private async Task SearchPatients()
        {
            try
            {
                IsLoading = true;
                SearchPerformed = true;
                StateHasChanged();

                // Map filter model to search criteria
                MapFilterToSearchCriteria();

                // Use real patient service
                if (PatientService != null)
                {
                    var patients = await PatientService.SearchPatientsAsync(SearchCriteria);
                    PatientResults = ConvertToPatientRegistryModels(patients);
                }
                else
                {
                    Logger?.LogWarning("PatientService is not available, using sample data");
                    PatientResults = GenerateSamplePatients();
                }

                var message = Localizer?["PatientsFound"] ?? "Found {0} patients";
                Snackbar?.Add(string.Format(message, TotalPatients), Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error searching patients: {Message}", ex.Message);
                var errorMessage = Localizer?["PatientSearchError"] ?? "Error searching patients";
                Snackbar?.Add(errorMessage, Severity.Error);
                PatientResults = new List<PatientRegistryModel>();
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }

        private void ClearFilters()
        {
            FilterModel = new DataHubFilterModel();
            SearchCriteria = new PatientSearchCriteria { OrganizationId = CurrentOrganizationId };
            PatientResults = new List<PatientRegistryModel>();
            SearchPerformed = false;
            StateHasChanged();

            var message = Localizer?["FiltersCleared"] ?? "Filters cleared";
            Snackbar?.Add(message, Severity.Info);
        }

        private async Task ExportData()
        {
            try
            {
                if (!HasResults)
                {
                    var noDataMessage = Localizer?["NoDataToExport"] ?? "No data to export";
                    Snackbar?.Add(noDataMessage, Severity.Warning);
                    return;
                }

                // Enhanced CSV export with all fields
                var csv = "Patient Name,Date of Birth,Sex,Age,Phone Number,Email,Address,PCP,Insurance,Status\n";
                foreach (var patient in PatientResults)
                {
                    csv += $"\"{patient.FullName}\"," +
                           $"\"{patient.DateOfBirth?.ToString("MM/dd/yyyy")}\"," +
                           $"\"{patient.Sex}\"," +
                           $"{patient.Age}," +
                           $"\"{patient.PhoneNumber}\"," +
                           $"\"{patient.Email}\"," +
                           $"\"{patient.Address}\"," +
                           $"\"{patient.PCP}\"," +
                           $"\"{patient.Insurance}\"," +
                           $"\"{(patient.IsActive ? "Active" : "Inactive")}\"\n";
                }

                // In a real implementation, this would trigger a file download
                // For now, we'll log the CSV content and show success message
                Logger?.LogInformation("CSV Export generated with {Count} records", TotalPatients);

                var successMessage = Localizer?["ExportCompleted"] ?? "Exported {0} patients to CSV";
                Snackbar?.Add(string.Format(successMessage, TotalPatients), Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error exporting data: {Message}", ex.Message);
                var errorMessage = Localizer?["ExportError"] ?? "Error exporting data";
                Snackbar?.Add(errorMessage, Severity.Error);
            }
        }

        private async Task ViewPatient(Guid patientId)
        {
            try
            {
                // Navigate to patient chart (placeholder)
                Snackbar?.Add($"Viewing patient: {patientId}", Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error viewing patient: {ex.Message}");
                Snackbar?.Add("Error viewing patient", Severity.Error);
            }
        }

        private async Task EditPatient(Guid patientId)
        {
            try
            {
                // Navigate to patient edit (placeholder)
                Snackbar?.Add($"Editing patient: {patientId}", Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error editing patient: {ex.Message}");
                Snackbar?.Add("Error editing patient", Severity.Error);
            }
        }

        private List<PatientRegistryModel> GenerateSamplePatients()
        {
            var patients = new List<PatientRegistryModel>();
            var random = new Random();
            var firstNames = new[] { "John", "Jane", "Michael", "Sarah", "David", "Lisa", "Robert", "Emily", "James", "Ashley" };
            var lastNames = new[] { "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez" };
            var sexes = new[] { "Male", "Female" };

            // Generate 5-15 sample patients
            var count = random.Next(5, 16);

            for (int i = 0; i < count; i++)
            {
                var firstName = firstNames[random.Next(firstNames.Length)];
                var lastName = lastNames[random.Next(lastNames.Length)];
                var sex = sexes[random.Next(sexes.Length)];
                var age = random.Next(18, 85);
                var dob = DateTime.Now.AddYears(-age).AddDays(random.Next(-365, 365));

                // Apply filters
                if (!string.IsNullOrEmpty(FilterModel.PatientName) &&
                    !$"{firstName} {lastName}".Contains(FilterModel.PatientName, StringComparison.OrdinalIgnoreCase))
                    continue;

                if (!string.IsNullOrEmpty(FilterModel.Sex) && FilterModel.Sex != "All" && sex != FilterModel.Sex)
                    continue;

                if (FilterModel.AgeFrom.HasValue && age < FilterModel.AgeFrom.Value)
                    continue;

                if (FilterModel.AgeTo.HasValue && age > FilterModel.AgeTo.Value)
                    continue;

                var insuranceProviders = new[] { "Blue Cross", "Aetna", "Cigna", "UnitedHealth", "Kaiser", "Humana" };
                var pcpNames = new[] { "Dr. Smith", "Dr. Johnson", "Dr. Williams", "Dr. Brown", "Dr. Davis" };
                var streets = new[] { "Main St", "Oak Ave", "Pine Rd", "Elm Dr", "Cedar Ln" };
                var cities = new[] { "Springfield", "Franklin", "Georgetown", "Madison", "Clinton" };
                var states = new[] { "CA", "NY", "TX", "FL", "IL" };

                patients.Add(new PatientRegistryModel
                {
                    Id = Guid.NewGuid(),
                    FullName = $"{firstName} {lastName}",
                    DateOfBirth = dob,
                    Sex = sex,
                    Age = age,
                    PhoneNumber = $"({random.Next(100, 999)}) {random.Next(100, 999)}-{random.Next(1000, 9999)}",
                    Email = $"{firstName.ToLower()}.{lastName.ToLower()}@email.com",
                    Address = $"{random.Next(100, 9999)} {streets[random.Next(streets.Length)]}, {cities[random.Next(cities.Length)]}, {states[random.Next(states.Length)]} {random.Next(10000, 99999)}",
                    PCP = pcpNames[random.Next(pcpNames.Length)],
                    Insurance = insuranceProviders[random.Next(insuranceProviders.Length)],
                    IsActive = random.Next(0, 10) > 1, // 90% active
                    CreatedAt = DateTime.Now.AddDays(-random.Next(1, 365)),
                    LastVisit = random.Next(0, 3) > 0 ? DateTime.Now.AddDays(-random.Next(1, 90)) : null
                });
            }

            return patients;
        }

        private void MapFilterToSearchCriteria()
        {
            SearchCriteria.AgeFrom = FilterModel.AgeFrom;
            SearchCriteria.AgeTo = FilterModel.AgeTo;
            SearchCriteria.Sex = FilterModel.Sex;
            SearchCriteria.PatientName = FilterModel.PatientName;
            SearchCriteria.PhoneNumber = FilterModel.PhoneNumber;
            SearchCriteria.Insurance = FilterModel.Insurance;
            SearchCriteria.DOBFrom = FilterModel.DOBFrom;
            SearchCriteria.DOBTo = FilterModel.DOBTo;
            SearchCriteria.PCP = FilterModel.PCP;
            SearchCriteria.Language = FilterModel.Language;
        }

        private List<PatientRegistryModel> ConvertToPatientRegistryModels(List<Patient> patients)
        {
            return patients.Select(p => new PatientRegistryModel
            {
                Id = p.Id,
                FullName = p.Name ?? "Unknown",
                DateOfBirth = p.DOB,
                Sex = p.Sex ?? "Unknown",
                Age = CalculateAge(p.DOB),
                PhoneNumber = p.PhoneNumber ?? "",
                Email = p.Email ?? "",
                Address = $"{p.Street}, {p.City}, {p.State} {p.PostalCode}".Trim(' ', ','),
                PCP = p.PCPName ?? "",
                Insurance = p.PrimaryInsuranceProvider ?? "",
                IsActive = true
            }).ToList();
        }

        private int CalculateAge(DateTime? dateOfBirth)
        {
            if (!dateOfBirth.HasValue) return 0;

            var today = DateTime.Today;
            var age = today.Year - dateOfBirth.Value.Year;

            if (dateOfBirth.Value.Date > today.AddYears(-age))
                age--;

            return age;
        }
    }

    // Enhanced model for patient registry with additional fields
    public class PatientRegistryModel
    {
        public Guid Id { get; set; }
        public string FullName { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string Sex { get; set; } = string.Empty;
        public int Age { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string PCP { get; set; } = string.Empty;
        public string Insurance { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastVisit { get; set; }
    }
}
