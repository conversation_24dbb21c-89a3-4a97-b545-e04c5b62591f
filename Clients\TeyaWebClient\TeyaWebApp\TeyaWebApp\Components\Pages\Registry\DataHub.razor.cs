using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaAIScribeResource;
using TeyaWebApp.Components.Pages.Registry.Models;
using Microsoft.AspNetCore.Components.Rendering;

namespace TeyaWebApp.Components.Pages.Registry
{
    public partial class DataHub : ComponentBase, IDisposable
    {
        [Inject] private ISnackbar? Snackbar { get; set; }
        [Inject] private ILogger<DataHub>? Logger { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; } = default!;
        [Inject] private IStringLocalizer<TeyaAIScribeResource.TeyaAIScribeResource>? Localizer { get; set; }
        [Inject] private IPatientService? PatientService { get; set; }
        [Inject] private IMemberService? MemberService { get; set; }

        // Filter properties using proper model
        private PatientSearchCriteria SearchCriteria = new();
        private DataHubFilterModel FilterModel = new();

        // Results properties
        private List<PatientRegistryModel> PatientResults = new();
        private List<PatientRegistryModel> PreviousResults = new();
        private HashSet<PatientRegistryModel> SelectedPatients = new();
        private bool IsLoading = true;
        private bool HasError = false;
        private string ErrorMessage = string.Empty;
        private bool SearchPerformed = false;
        private bool HasResults => PatientResults?.Any() == true;
        private bool HasPreviousResults => PreviousResults?.Any() == true;
        private bool HasSelectedPatients => SelectedPatients?.Any() == true;
        private int TotalPatients => PatientResults?.Count ?? 0;

        // Tab management
        private int ActiveTabIndex = 0;
        private bool IsProcessing = false;

        // Registry lock system
        private bool IsRegistryLocked = false;
        private bool IsCurrentUserLockHolder = false;
        private DateTime? LockAcquiredTime = null;
        private Timer? LockTimer = null;
        private const int LockTimeoutMinutes = 15;

        // Organization context
        private Guid CurrentOrganizationId = Guid.Empty;

        // Tab filter models
        private DemographicsFilterModel DemographicsFilter = new();
        private VitalsFilterModel VitalsFilter = new();
        private LabsFilterModel LabsFilter = new();
        private ICDFilterModel ICDFilter = new();
        private CPTFilterModel CPTFilter = new();
        private RxFilterModel RxFilter = new();
        private ImmunizationFilterModel ImmunizationFilter = new();
        private EncountersFilterModel EncountersFilter = new();
        private AllergiesFilterModel AllergiesFilter = new();
        private MedicalHistoryFilterModel MedicalHistoryFilter = new();
        private StructuredDataFilterModel StructuredDataFilter = new();
        private ReferralsFilterModel ReferralsFilter = new();
        private ReportsFilterModel ReportsFilter = new();
        private SavedReportsFilterModel SavedReportsFilter = new();

        private void RefreshPage()
        {
            NavigationManager.Refresh(true);
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                IsLoading = true;
                HasError = false;
                StateHasChanged();

                // Initialize organization context
                // In a real implementation, this would come from user context/session
                CurrentOrganizationId = Guid.NewGuid(); // Placeholder - should come from authenticated user context

                // Initialize search criteria
                SearchCriteria.OrganizationId = CurrentOrganizationId;

                // Attempt to acquire registry lock
                await AcquireRegistryLock();

                IsLoading = false;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error initializing DataHub: {Message}", ex.Message);
                HasError = true;
                ErrorMessage = Localizer?["DataHubInitializationError"] ?? "Error initializing DataHub";
                IsLoading = false;
                StateHasChanged();
            }
        }

        public void Dispose()
        {
            LockTimer?.Dispose();
            if (IsCurrentUserLockHolder)
            {
                _ = Task.Run(async () => await ReleaseLock());
            }
        }

        // Registry Lock Management
        private async Task AcquireRegistryLock()
        {
            try
            {
                // In a real implementation, this would check with a service/database
                // For now, simulate lock acquisition
                IsRegistryLocked = false; // Simulate no other user has lock
                IsCurrentUserLockHolder = true;
                LockAcquiredTime = DateTime.Now;

                // Set up timer for lock timeout
                LockTimer = new Timer(OnLockTimeout, null, TimeSpan.FromMinutes(LockTimeoutMinutes), TimeSpan.FromMinutes(1));

                Logger?.LogInformation("Registry lock acquired at {Time}", LockAcquiredTime);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error acquiring registry lock: {Message}", ex.Message);
                IsRegistryLocked = true;
                IsCurrentUserLockHolder = false;
            }
        }

        private async Task ReleaseLock()
        {
            try
            {
                IsCurrentUserLockHolder = false;
                IsRegistryLocked = false;
                LockAcquiredTime = null;
                LockTimer?.Dispose();
                LockTimer = null;

                var message = Localizer?["LockReleased"] ?? "Registry lock released";
                Snackbar?.Add(message, Severity.Info);

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error releasing registry lock: {Message}", ex.Message);
            }
        }

        private async Task RequestLockRelease()
        {
            try
            {
                // In a real implementation, this would send a request to the current lock holder
                var message = Localizer?["LockReleaseRequested"] ?? "Lock release request sent";
                Snackbar?.Add(message, Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error requesting lock release: {Message}", ex.Message);
            }
        }

        private void OnLockTimeout(object? state)
        {
            if (LockAcquiredTime.HasValue && DateTime.Now.Subtract(LockAcquiredTime.Value).TotalMinutes >= LockTimeoutMinutes)
            {
                _ = Task.Run(async () => await ReleaseLock());
            }
        }

        // Query Operations
        private async Task RunNewQuery()
        {
            try
            {
                IsProcessing = true;
                SearchPerformed = true;
                StateHasChanged();

                // Store previous results for subset operations
                PreviousResults = new List<PatientRegistryModel>(PatientResults);

                // Map current tab filters to search criteria
                MapCurrentTabToSearchCriteria();

                // Execute search
                await ExecuteSearch();

                var message = Localizer?["NewQueryCompleted"] ?? "New query completed. Found {0} patients";
                Snackbar?.Add(string.Format(message, TotalPatients), Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error running new query: {Message}", ex.Message);
                var errorMessage = Localizer?["QueryError"] ?? "Error executing query";
                Snackbar?.Add(errorMessage, Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }

        private async Task RunSubsetQuery()
        {
            try
            {
                if (!HasPreviousResults)
                {
                    var message = Localizer?["NoPreviousResults"] ?? "No previous results to filter";
                    Snackbar?.Add(message, Severity.Warning);
                    return;
                }

                IsProcessing = true;
                StateHasChanged();

                // Apply current tab filters to previous results
                var filteredResults = ApplyFiltersToResults(PreviousResults);
                PatientResults = filteredResults;

                var message = Localizer?["SubsetQueryCompleted"] ?? "Subset query completed. Found {0} patients from previous results";
                Snackbar?.Add(string.Format(message, TotalPatients), Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error running subset query: {Message}", ex.Message);
                var errorMessage = Localizer?["QueryError"] ?? "Error executing subset query";
                Snackbar?.Add(errorMessage, Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }

        private async Task RunSubsetNotQuery()
        {
            try
            {
                if (!HasPreviousResults)
                {
                    var message = Localizer?["NoPreviousResults"] ?? "No previous results to filter";
                    Snackbar?.Add(message, Severity.Warning);
                    return;
                }

                IsProcessing = true;
                StateHasChanged();

                // Apply inverse filters to previous results
                var filteredResults = ApplyInverseFiltersToResults(PreviousResults);
                PatientResults = filteredResults;

                var message = Localizer?["SubsetNotQueryCompleted"] ?? "Subset (NOT) query completed. Found {0} patients excluding criteria";
                Snackbar?.Add(string.Format(message, TotalPatients), Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error running subset NOT query: {Message}", ex.Message);
                var errorMessage = Localizer?["QueryError"] ?? "Error executing subset NOT query";
                Snackbar?.Add(errorMessage, Severity.Error);
            }
            finally
            {
                IsProcessing = false;
                StateHasChanged();
            }
        }

        private async Task SaveQueries()
        {
            try
            {
                if (!HasResults)
                {
                    var message = Localizer?["NoResultsToSave"] ?? "No results to save";
                    Snackbar?.Add(message, Severity.Warning);
                    return;
                }

                // In a real implementation, this would save the query criteria and results
                var queryName = $"Query_{DateTime.Now:yyyyMMdd_HHmmss}";
                Logger?.LogInformation("Saving query: {QueryName} with {Count} results", queryName, TotalPatients);

                var message = Localizer?["QuerySaved"] ?? "Query saved successfully";
                Snackbar?.Add(message, Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error saving query: {Message}", ex.Message);
                var errorMessage = Localizer?["SaveQueryError"] ?? "Error saving query";
                Snackbar?.Add(errorMessage, Severity.Error);
            }
        }

        private async Task SearchPatients()
        {
            await RunNewQuery();
        }

        private void ClearFilters()
        {
            // Clear all tab filters
            DemographicsFilter = new();
            VitalsFilter = new();
            LabsFilter = new();
            ICDFilter = new();
            CPTFilter = new();
            RxFilter = new();
            ImmunizationFilter = new();
            EncountersFilter = new();
            AllergiesFilter = new();
            MedicalHistoryFilter = new();
            StructuredDataFilter = new();
            ReferralsFilter = new();
            ReportsFilter = new();
            SavedReportsFilter = new();

            FilterModel = new DataHubFilterModel();
            SearchCriteria = new PatientSearchCriteria { OrganizationId = CurrentOrganizationId };
            PatientResults = new List<PatientRegistryModel>();
            PreviousResults = new List<PatientRegistryModel>();
            SelectedPatients = new HashSet<PatientRegistryModel>();
            SearchPerformed = false;
            StateHasChanged();

            var message = Localizer?["FiltersCleared"] ?? "All filters cleared";
            Snackbar?.Add(message, Severity.Info);
        }

        // Patient Action Methods
        private async Task ExcludeFromSearch()
        {
            try
            {
                if (!HasSelectedPatients)
                {
                    var message = Localizer?["NoPatientSelected"] ?? "Please select patients to exclude";
                    Snackbar?.Add(message, Severity.Warning);
                    return;
                }

                // In a real implementation, this would mark patients as excluded in the database
                var excludedCount = SelectedPatients.Count;
                Logger?.LogInformation("Excluding {Count} patients from search", excludedCount);

                var message = Localizer?["PatientsExcluded"] ?? "{0} patients excluded from future searches";
                Snackbar?.Add(string.Format(message, excludedCount), Severity.Success);

                SelectedPatients.Clear();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error excluding patients: {Message}", ex.Message);
                var errorMessage = Localizer?["ExcludeError"] ?? "Error excluding patients";
                Snackbar?.Add(errorMessage, Severity.Error);
            }
        }

        private async Task InactivatePatients()
        {
            try
            {
                if (!HasSelectedPatients)
                {
                    var message = Localizer?["NoPatientSelected"] ?? "Please select patients to inactivate";
                    Snackbar?.Add(message, Severity.Warning);
                    return;
                }

                var inactivatedCount = SelectedPatients.Count;
                foreach (var patient in SelectedPatients)
                {
                    patient.IsActive = false;
                }

                var message = Localizer?["PatientsInactivated"] ?? "{0} patients inactivated";
                Snackbar?.Add(string.Format(message, inactivatedCount), Severity.Success);

                SelectedPatients.Clear();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error inactivating patients: {Message}", ex.Message);
                var errorMessage = Localizer?["InactivateError"] ?? "Error inactivating patients";
                Snackbar?.Add(errorMessage, Severity.Error);
            }
        }

        private async Task ActivatePatients()
        {
            try
            {
                if (!HasSelectedPatients)
                {
                    var message = Localizer?["NoPatientSelected"] ?? "Please select patients to activate";
                    Snackbar?.Add(message, Severity.Warning);
                    return;
                }

                var activatedCount = SelectedPatients.Count;
                foreach (var patient in SelectedPatients)
                {
                    patient.IsActive = true;
                }

                var message = Localizer?["PatientsActivated"] ?? "{0} patients activated";
                Snackbar?.Add(string.Format(message, activatedCount), Severity.Success);

                SelectedPatients.Clear();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error activating patients: {Message}", ex.Message);
                var errorMessage = Localizer?["ActivateError"] ?? "Error activating patients";
                Snackbar?.Add(errorMessage, Severity.Error);
            }
        }

        private async Task ShowExcludedPatients()
        {
            try
            {
                // In a real implementation, this would query excluded patients
                var message = Localizer?["ShowingExcludedPatients"] ?? "Showing excluded patients";
                Snackbar?.Add(message, Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error showing excluded patients: {Message}", ex.Message);
            }
        }

        private async Task CopyResults()
        {
            try
            {
                if (!HasResults)
                {
                    var message = Localizer?["NoResultsToCopy"] ?? "No results to copy";
                    Snackbar?.Add(message, Severity.Warning);
                    return;
                }

                // In a real implementation, this would copy to clipboard
                var message = Localizer?["ResultsCopied"] ?? "Results copied to clipboard";
                Snackbar?.Add(message, Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error copying results: {Message}", ex.Message);
            }
        }

        private async Task ExportResults()
        {
            await ExportData();
        }

        private async Task AnalyzeData()
        {
            try
            {
                if (!HasResults)
                {
                    var message = Localizer?["NoDataToAnalyze"] ?? "No data to analyze";
                    Snackbar?.Add(message, Severity.Warning);
                    return;
                }

                // In a real implementation, this would open analysis window
                var message = Localizer?["OpeningAnalysis"] ?? "Opening data analysis window";
                Snackbar?.Add(message, Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error analyzing data: {Message}", ex.Message);
            }
        }

        // Patient Integration Methods
        private async Task LaunchPatientHub(Guid patientId)
        {
            try
            {
                // In a real implementation, this would navigate to patient hub
                var message = Localizer?["LaunchingPatientHub"] ?? "Launching Patient Hub";
                Snackbar?.Add(message, Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error launching patient hub: {Message}", ex.Message);
            }
        }

        private async Task NewAppointment(Guid patientId)
        {
            try
            {
                // In a real implementation, this would open appointment scheduling
                var message = Localizer?["OpeningAppointmentScheduler"] ?? "Opening appointment scheduler";
                Snackbar?.Add(message, Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error opening appointment scheduler: {Message}", ex.Message);
            }
        }

        private async Task OpenFlowsheet(Guid patientId)
        {
            try
            {
                // In a real implementation, this would open patient flowsheet
                var message = Localizer?["OpeningFlowsheet"] ?? "Opening patient flowsheet";
                Snackbar?.Add(message, Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error opening flowsheet: {Message}", ex.Message);
            }
        }

        private async Task SendMessage(Guid patientId)
        {
            try
            {
                // In a real implementation, this would open messenger
                var message = Localizer?["OpeningMessenger"] ?? "Opening messenger";
                Snackbar?.Add(message, Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error opening messenger: {Message}", ex.Message);
            }
        }

        private async Task ExportData()
        {
            try
            {
                if (!HasResults)
                {
                    var noDataMessage = Localizer?["NoDataToExport"] ?? "No data to export";
                    Snackbar?.Add(noDataMessage, Severity.Warning);
                    return;
                }

                // Enhanced CSV export with all fields
                var csv = "Patient Name,Date of Birth,Sex,Age,Phone Number,Email,Address,PCP,Insurance,Status\n";
                foreach (var patient in PatientResults)
                {
                    csv += $"\"{patient.FullName}\"," +
                           $"\"{patient.DateOfBirth?.ToString("MM/dd/yyyy")}\"," +
                           $"\"{patient.Sex}\"," +
                           $"{patient.Age}," +
                           $"\"{patient.PhoneNumber}\"," +
                           $"\"{patient.Email}\"," +
                           $"\"{patient.Address}\"," +
                           $"\"{patient.PCP}\"," +
                           $"\"{patient.Insurance}\"," +
                           $"\"{(patient.IsActive ? "Active" : "Inactive")}\"\n";
                }

                // In a real implementation, this would trigger a file download
                // For now, we'll log the CSV content and show success message
                Logger?.LogInformation("CSV Export generated with {Count} records", TotalPatients);

                var successMessage = Localizer?["ExportCompleted"] ?? "Exported {0} patients to CSV";
                Snackbar?.Add(string.Format(successMessage, TotalPatients), Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError(ex, "Error exporting data: {Message}", ex.Message);
                var errorMessage = Localizer?["ExportError"] ?? "Error exporting data";
                Snackbar?.Add(errorMessage, Severity.Error);
            }
        }

        private async Task ViewPatient(Guid patientId)
        {
            try
            {
                // Navigate to patient chart (placeholder)
                Snackbar?.Add($"Viewing patient: {patientId}", Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error viewing patient: {ex.Message}");
                Snackbar?.Add("Error viewing patient", Severity.Error);
            }
        }

        private async Task EditPatient(Guid patientId)
        {
            try
            {
                // Navigate to patient edit (placeholder)
                Snackbar?.Add($"Editing patient: {patientId}", Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error editing patient: {ex.Message}");
                Snackbar?.Add("Error editing patient", Severity.Error);
            }
        }

        private List<PatientRegistryModel> GenerateSamplePatients()
        {
            var patients = new List<PatientRegistryModel>();
            var random = new Random();
            var firstNames = new[] { "John", "Jane", "Michael", "Sarah", "David", "Lisa", "Robert", "Emily", "James", "Ashley" };
            var lastNames = new[] { "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez" };
            var sexes = new[] { "Male", "Female" };

            // Generate 5-15 sample patients
            var count = random.Next(5, 16);

            for (int i = 0; i < count; i++)
            {
                var firstName = firstNames[random.Next(firstNames.Length)];
                var lastName = lastNames[random.Next(lastNames.Length)];
                var sex = sexes[random.Next(sexes.Length)];
                var age = random.Next(18, 85);
                var dob = DateTime.Now.AddYears(-age).AddDays(random.Next(-365, 365));

                // Apply filters
                if (!string.IsNullOrEmpty(FilterModel.PatientName) &&
                    !$"{firstName} {lastName}".Contains(FilterModel.PatientName, StringComparison.OrdinalIgnoreCase))
                    continue;

                if (!string.IsNullOrEmpty(FilterModel.Sex) && FilterModel.Sex != "All" && sex != FilterModel.Sex)
                    continue;

                if (FilterModel.AgeFrom.HasValue && age < FilterModel.AgeFrom.Value)
                    continue;

                if (FilterModel.AgeTo.HasValue && age > FilterModel.AgeTo.Value)
                    continue;

                var insuranceProviders = new[] { "Blue Cross", "Aetna", "Cigna", "UnitedHealth", "Kaiser", "Humana" };
                var pcpNames = new[] { "Dr. Smith", "Dr. Johnson", "Dr. Williams", "Dr. Brown", "Dr. Davis" };
                var streets = new[] { "Main St", "Oak Ave", "Pine Rd", "Elm Dr", "Cedar Ln" };
                var cities = new[] { "Springfield", "Franklin", "Georgetown", "Madison", "Clinton" };
                var states = new[] { "CA", "NY", "TX", "FL", "IL" };

                patients.Add(new PatientRegistryModel
                {
                    Id = Guid.NewGuid(),
                    FullName = $"{firstName} {lastName}",
                    DateOfBirth = dob,
                    Sex = sex,
                    Age = age,
                    PhoneNumber = $"({random.Next(100, 999)}) {random.Next(100, 999)}-{random.Next(1000, 9999)}",
                    Email = $"{firstName.ToLower()}.{lastName.ToLower()}@email.com",
                    Address = $"{random.Next(100, 9999)} {streets[random.Next(streets.Length)]}, {cities[random.Next(cities.Length)]}, {states[random.Next(states.Length)]} {random.Next(10000, 99999)}",
                    PCP = pcpNames[random.Next(pcpNames.Length)],
                    Insurance = insuranceProviders[random.Next(insuranceProviders.Length)],
                    IsActive = random.Next(0, 10) > 1, // 90% active
                    CreatedAt = DateTime.Now.AddDays(-random.Next(1, 365)),
                    LastVisit = random.Next(0, 3) > 0 ? DateTime.Now.AddDays(-random.Next(1, 90)) : null
                });
            }

            return patients;
        }

        private void MapFilterToSearchCriteria()
        {
            SearchCriteria.AgeFrom = FilterModel.AgeFrom;
            SearchCriteria.AgeTo = FilterModel.AgeTo;
            SearchCriteria.Sex = FilterModel.Sex;
            SearchCriteria.PatientName = FilterModel.PatientName;
            SearchCriteria.PhoneNumber = FilterModel.PhoneNumber;
            SearchCriteria.Insurance = FilterModel.Insurance;
            SearchCriteria.DOBFrom = FilterModel.DOBFrom;
            SearchCriteria.DOBTo = FilterModel.DOBTo;
            SearchCriteria.PCP = FilterModel.PCP;
            SearchCriteria.Language = FilterModel.Language;
        }

        private List<PatientRegistryModel> ConvertToPatientRegistryModels(List<Patient> patients)
        {
            return patients.Select(p => new PatientRegistryModel
            {
                Id = p.Id,
                FullName = p.Name ?? "Unknown",
                DateOfBirth = p.DOB,
                Sex = p.Sex ?? "Unknown",
                Age = CalculateAge(p.DOB),
                PhoneNumber = p.PhoneNumber ?? "",
                Email = p.Email ?? "",
                Address = $"{p.Street}, {p.City}, {p.State} {p.PostalCode}".Trim(' ', ','),
                PCP = p.PCPName ?? "",
                Insurance = p.PrimaryInsuranceProvider ?? "",
                IsActive = true
            }).ToList();
        }

        private int CalculateAge(DateTime? dateOfBirth)
        {
            if (!dateOfBirth.HasValue) return 0;

            var today = DateTime.Today;
            var age = today.Year - dateOfBirth.Value.Year;

            if (dateOfBirth.Value.Date > today.AddYears(-age))
                age--;

            return age;
        }

        // Tab Rendering Methods
        private async Task<RenderFragment> RenderDemographicsTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");

                // Demographics filters
                builder.OpenComponent<MudGrid>(2);

                // Age Range
                builder.OpenComponent<MudItem>(3);
                builder.AddAttribute(4, "xs", 12);
                builder.AddAttribute(5, "sm", 6);
                builder.AddAttribute(6, "md", 3);
                builder.AddAttribute(7, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.OpenComponent<MudTextField<int?>>(0);
                    childBuilder.AddAttribute(1, "Value", DemographicsFilter.AgeFrom);
                    childBuilder.AddAttribute(2, "ValueChanged", EventCallback.Factory.Create<int?>(this, value => DemographicsFilter.AgeFrom = value));
                    childBuilder.AddAttribute(3, "Label", Localizer?["AgeFrom"] ?? "Age From");
                    childBuilder.AddAttribute(4, "Variant", Variant.Outlined);
                    childBuilder.AddAttribute(5, "Margin", Margin.Dense);
                    childBuilder.CloseComponent();
                }));
                builder.CloseComponent();

                builder.OpenComponent<MudItem>(8);
                builder.AddAttribute(9, "xs", 12);
                builder.AddAttribute(10, "sm", 6);
                builder.AddAttribute(11, "md", 3);
                builder.AddAttribute(12, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.OpenComponent<MudTextField<int?>>(0);
                    childBuilder.AddAttribute(1, "Value", DemographicsFilter.AgeTo);
                    childBuilder.AddAttribute(2, "ValueChanged", EventCallback.Factory.Create<int?>(this, value => DemographicsFilter.AgeTo = value));
                    childBuilder.AddAttribute(3, "Label", Localizer?["AgeTo"] ?? "Age To");
                    childBuilder.AddAttribute(4, "Variant", Variant.Outlined);
                    childBuilder.AddAttribute(5, "Margin", Margin.Dense);
                    childBuilder.CloseComponent();
                }));
                builder.CloseComponent();

                // Sex
                builder.OpenComponent<MudItem>(13);
                builder.AddAttribute(14, "xs", 12);
                builder.AddAttribute(15, "sm", 6);
                builder.AddAttribute(16, "md", 3);
                builder.AddAttribute(17, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.OpenComponent<MudSelect<string>>(0);
                    childBuilder.AddAttribute(1, "Value", DemographicsFilter.Sex);
                    childBuilder.AddAttribute(2, "ValueChanged", EventCallback.Factory.Create<string>(this, value => DemographicsFilter.Sex = value));
                    childBuilder.AddAttribute(3, "Label", Localizer?["Sex"] ?? "Sex");
                    childBuilder.AddAttribute(4, "Variant", Variant.Outlined);
                    childBuilder.AddAttribute(5, "Margin", Margin.Dense);
                    childBuilder.AddAttribute(6, "ChildContent", (RenderFragment)(selectBuilder =>
                    {
                        selectBuilder.OpenComponent<MudSelectItem<string>>(0);
                        selectBuilder.AddAttribute(1, "Value", "");
                        selectBuilder.AddAttribute(2, "ChildContent", (RenderFragment)(itemBuilder =>
                        {
                            itemBuilder.AddContent(0, Localizer?["All"] ?? "All");
                        }));
                        selectBuilder.CloseComponent();

                        selectBuilder.OpenComponent<MudSelectItem<string>>(3);
                        selectBuilder.AddAttribute(4, "Value", "Male");
                        selectBuilder.AddAttribute(5, "ChildContent", (RenderFragment)(itemBuilder =>
                        {
                            itemBuilder.AddContent(0, Localizer?["Male"] ?? "Male");
                        }));
                        selectBuilder.CloseComponent();

                        selectBuilder.OpenComponent<MudSelectItem<string>>(6);
                        selectBuilder.AddAttribute(7, "Value", "Female");
                        selectBuilder.AddAttribute(8, "ChildContent", (RenderFragment)(itemBuilder =>
                        {
                            itemBuilder.AddContent(0, Localizer?["Female"] ?? "Female");
                        }));
                        selectBuilder.CloseComponent();

                        selectBuilder.OpenComponent<MudSelectItem<string>>(9);
                        selectBuilder.AddAttribute(10, "Value", "Other");
                        selectBuilder.AddAttribute(11, "ChildContent", (RenderFragment)(itemBuilder =>
                        {
                            itemBuilder.AddContent(0, Localizer?["Other"] ?? "Other");
                        }));
                        selectBuilder.CloseComponent();
                    }));
                    childBuilder.CloseComponent();
                }));
                builder.CloseComponent();

                // Patient Name
                builder.OpenComponent<MudItem>(18);
                builder.AddAttribute(19, "xs", 12);
                builder.AddAttribute(20, "sm", 6);
                builder.AddAttribute(21, "md", 3);
                builder.AddAttribute(22, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.OpenComponent<MudTextField<string>>(0);
                    childBuilder.AddAttribute(1, "Value", DemographicsFilter.PatientName);
                    childBuilder.AddAttribute(2, "ValueChanged", EventCallback.Factory.Create<string>(this, value => DemographicsFilter.PatientName = value));
                    childBuilder.AddAttribute(3, "Label", Localizer?["PatientName"] ?? "Patient Name");
                    childBuilder.AddAttribute(4, "Variant", Variant.Outlined);
                    childBuilder.AddAttribute(5, "Margin", Margin.Dense);
                    childBuilder.CloseComponent();
                }));
                builder.CloseComponent();

                // Insurance
                builder.OpenComponent<MudItem>(23);
                builder.AddAttribute(24, "xs", 12);
                builder.AddAttribute(25, "sm", 6);
                builder.AddAttribute(26, "md", 3);
                builder.AddAttribute(27, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.OpenComponent<MudTextField<string>>(0);
                    childBuilder.AddAttribute(1, "Value", DemographicsFilter.Insurance);
                    childBuilder.AddAttribute(2, "ValueChanged", EventCallback.Factory.Create<string>(this, value => DemographicsFilter.Insurance = value));
                    childBuilder.AddAttribute(3, "Label", Localizer?["Insurance"] ?? "Insurance");
                    childBuilder.AddAttribute(4, "Variant", Variant.Outlined);
                    childBuilder.AddAttribute(5, "Margin", Margin.Dense);
                    childBuilder.CloseComponent();
                }));
                builder.CloseComponent();

                builder.CloseComponent(); // MudGrid
                builder.CloseElement(); // div
            };
        }

        private async Task<RenderFragment> RenderVitalsTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["VitalsTabComingSoon"] ?? "Vitals tab functionality coming soon. This will include height, weight, blood pressure, heart rate, temperature, BMI, respiratory rate, pain score, glucose, and peak flow filters with date ranges.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderLabsTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["LabsTabComingSoon"] ?? "Labs/DI/Procedure tab functionality coming soon. This will include lab results, imaging, procedures with attributes, fasting conditions, and result date ranges.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderICDTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["ICDTabComingSoon"] ?? "ICD tab functionality coming soon. This will include ICD codes/groups search with assessments and problem list options.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderCPTTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["CPTTabComingSoon"] ?? "CPT tab functionality coming soon. This will include CPT codes/groups with billing categories and fee filters.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderRxTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["RxTabComingSoon"] ?? "Rx tab functionality coming soon. This will include medication search by drug name/class with prescription date ranges.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderImmunizationTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["ImmunizationTabComingSoon"] ?? "Immunization tab functionality coming soon. This will include vaccine tracking with CVX codes, lot numbers, and shot counts.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderEncountersTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["EncountersTabComingSoon"] ?? "Encounters tab functionality coming soon. This will include visit history with providers, facilities, visit types, and date ranges.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderAllergiesTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["AllergiesTabComingSoon"] ?? "Allergies tab functionality coming soon. This will include allergy search with structured/non-structured data.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderMedicalHistoryTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["MedicalHistoryTabComingSoon"] ?? "Medical History tab functionality coming soon. This will include medical conditions search in active problems or past history.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderStructuredDataTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["StructuredDataTabComingSoon"] ?? "Structured Data tab functionality coming soon. This will include custom field queries.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderReferralsTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["ReferralsTabComingSoon"] ?? "Referrals tab functionality coming soon. This will include incoming/outgoing referrals with specialty and ICD filtering.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderReportsTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["ReportsTabComingSoon"] ?? "Reports tab functionality coming soon. This will include chronic care reports integration.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        private async Task<RenderFragment> RenderSavedReportsTab()
        {
            return builder =>
            {
                builder.OpenElement(0, "div");
                builder.AddAttribute(1, "class", "tab-content");
                builder.OpenComponent<MudAlert>(2);
                builder.AddAttribute(3, "Severity", Severity.Info);
                builder.AddAttribute(4, "ChildContent", (RenderFragment)(childBuilder =>
                {
                    childBuilder.AddContent(0, Localizer?["SavedReportsTabComingSoon"] ?? "Saved Reports tab functionality coming soon. This will include manage and run saved queries.");
                }));
                builder.CloseComponent();
                builder.CloseElement();
            };
        }

        // Helper Methods for Query Operations
        private void MapCurrentTabToSearchCriteria()
        {
            // Map the active tab's filter to search criteria
            switch (ActiveTabIndex)
            {
                case 0: // Demographics
                    MapDemographicsToSearchCriteria();
                    break;
                case 1: // Vitals
                    MapVitalsToSearchCriteria();
                    break;
                // Add other cases as tabs are implemented
                default:
                    MapDemographicsToSearchCriteria(); // Default to demographics
                    break;
            }
        }

        private void MapDemographicsToSearchCriteria()
        {
            SearchCriteria.AgeFrom = DemographicsFilter.AgeFrom;
            SearchCriteria.AgeTo = DemographicsFilter.AgeTo;
            SearchCriteria.Sex = DemographicsFilter.Sex;
            SearchCriteria.PatientName = DemographicsFilter.PatientName;
            SearchCriteria.PhoneNumber = DemographicsFilter.PhoneNumber;
            SearchCriteria.Insurance = DemographicsFilter.Insurance;
            SearchCriteria.DOBFrom = DemographicsFilter.DOBFrom;
            SearchCriteria.DOBTo = DemographicsFilter.DOBTo;
            SearchCriteria.PCP = DemographicsFilter.PCP;
            SearchCriteria.Language = DemographicsFilter.Language;
        }

        private void MapVitalsToSearchCriteria()
        {
            // Placeholder for vitals mapping
            // Will be implemented when vitals tab is fully developed
        }

        private async Task ExecuteSearch()
        {
            // Use real patient service
            if (PatientService != null)
            {
                var patients = await PatientService.SearchPatientsAsync(SearchCriteria);
                PatientResults = ConvertToPatientRegistryModels(patients);
            }
            else
            {
                Logger?.LogWarning("PatientService is not available, using sample data");
                PatientResults = GenerateSamplePatients();
            }
        }

        private List<PatientRegistryModel> ApplyFiltersToResults(List<PatientRegistryModel> sourceResults)
        {
            // Apply current tab filters to the source results
            var filteredResults = sourceResults.AsQueryable();

            // Apply demographics filters if on demographics tab
            if (ActiveTabIndex == 0)
            {
                if (DemographicsFilter.AgeFrom.HasValue)
                    filteredResults = filteredResults.Where(p => p.Age >= DemographicsFilter.AgeFrom.Value);

                if (DemographicsFilter.AgeTo.HasValue)
                    filteredResults = filteredResults.Where(p => p.Age <= DemographicsFilter.AgeTo.Value);

                if (!string.IsNullOrEmpty(DemographicsFilter.Sex) && DemographicsFilter.Sex != "All")
                    filteredResults = filteredResults.Where(p => p.Sex == DemographicsFilter.Sex);

                if (!string.IsNullOrEmpty(DemographicsFilter.PatientName))
                    filteredResults = filteredResults.Where(p => p.FullName.Contains(DemographicsFilter.PatientName, StringComparison.OrdinalIgnoreCase));

                if (!string.IsNullOrEmpty(DemographicsFilter.Insurance))
                    filteredResults = filteredResults.Where(p => p.Insurance.Contains(DemographicsFilter.Insurance, StringComparison.OrdinalIgnoreCase));
            }

            return filteredResults.ToList();
        }

        private List<PatientRegistryModel> ApplyInverseFiltersToResults(List<PatientRegistryModel> sourceResults)
        {
            // Apply inverse of current tab filters to the source results
            var filteredResults = sourceResults.AsQueryable();

            // Apply inverse demographics filters if on demographics tab
            if (ActiveTabIndex == 0)
            {
                if (DemographicsFilter.AgeFrom.HasValue)
                    filteredResults = filteredResults.Where(p => p.Age < DemographicsFilter.AgeFrom.Value);

                if (DemographicsFilter.AgeTo.HasValue)
                    filteredResults = filteredResults.Where(p => p.Age > DemographicsFilter.AgeTo.Value);

                if (!string.IsNullOrEmpty(DemographicsFilter.Sex) && DemographicsFilter.Sex != "All")
                    filteredResults = filteredResults.Where(p => p.Sex != DemographicsFilter.Sex);

                if (!string.IsNullOrEmpty(DemographicsFilter.PatientName))
                    filteredResults = filteredResults.Where(p => !p.FullName.Contains(DemographicsFilter.PatientName, StringComparison.OrdinalIgnoreCase));

                if (!string.IsNullOrEmpty(DemographicsFilter.Insurance))
                    filteredResults = filteredResults.Where(p => !p.Insurance.Contains(DemographicsFilter.Insurance, StringComparison.OrdinalIgnoreCase));
            }

            return filteredResults.ToList();
        }
    }

    // Enhanced model for patient registry with additional fields
    public class PatientRegistryModel
    {
        public Guid Id { get; set; }
        public string FullName { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string Sex { get; set; } = string.Empty;
        public int Age { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string PCP { get; set; } = string.Empty;
        public string Insurance { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastVisit { get; set; }
    }
}
