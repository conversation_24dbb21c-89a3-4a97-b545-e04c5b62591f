﻿var BlazorAudioRecorder = {};
(function () {
    let mediaStream;
    let audioContext;
    let processor;
    let analyser;
    let caller;
    let mMediaRecorder;
    let mAudioChunks = [];
    let animationFrameId;
    let lastUpdateTime = 0;
    const updateInterval = 100;
    const silenceThreshold = 0.02;
    let isAudioDetected = false;

    BlazorAudioRecorder.Initialize = function (vCaller) {
        caller = vCaller;
    };

    function startVisualization() {
        function analyze() {
            if (!analyser) return;

            const now = performance.now();
            if (now - lastUpdateTime < updateInterval) {
                animationFrameId = requestAnimationFrame(analyze);
                return;
            }
            lastUpdateTime = now;

            const bufferLength = analyser.frequencyBinCount;
            const dataArray = new Float32Array(bufferLength);
            analyser.getFloatTimeDomainData(dataArray);

            const currentDetection = detectAudio(dataArray);

            if (currentDetection !== isAudioDetected && caller) {
                isAudioDetected = currentDetection;
                caller.invokeMethodAsync('OnAudioDetected', isAudioDetected);
            }

            animationFrameId = requestAnimationFrame(analyze);
        }

        analyze();
    }

    function detectAudio(dataArray) {
        for (let i = 0; i < dataArray.length; i++) {
            if (Math.abs(dataArray[i]) > silenceThreshold) {
                return true;
            }
        }
        return false;
    }

    function stopVisualization() {
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
        }
        isAudioDetected = false;
    }

    BlazorAudioRecorder.StartRecord = async function () {
        try {
            mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: false
                }
            });

            audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: 16000
            });

            analyser = audioContext.createAnalyser();
            analyser.fftSize = 256;
            analyser.smoothingTimeConstant = 0.3;

            if (processor) {
                processor.disconnect();
                processor = null;
            }

            processor = audioContext.createScriptProcessor(2048, 1, 1);
            processor.onaudioprocess = function (e) {
                if (!caller) return;
                const audioData = e.inputBuffer.getChannelData(0);
                const pcmData = floatTo16BitPCM(audioData);
                const base64String = arrayBufferToBase64(pcmData.buffer);
                caller.invokeMethodAsync('ProcessAudioChunk', base64String);
            };

            const source = audioContext.createMediaStreamSource(mediaStream);
            source.connect(analyser);
            source.connect(processor);
            processor.connect(audioContext.destination);

            mMediaRecorder = new MediaRecorder(mediaStream, {
                mimeType: 'audio/webm;codecs=opus',  // WebM with Opus codec is very efficient for voice
                audioBitsPerSecond: 16000            // Low bitrate for voice (16kbps)
            });
            startVisualization();
            mAudioChunks = [];

            mMediaRecorder.addEventListener('dataavailable', vEvent => {
                mAudioChunks.push(vEvent.data);
            });

            mMediaRecorder.addEventListener('stop', () => {
                stopVisualization();
            });

            mMediaRecorder.start();

        } catch (error) {
            console.error('Error starting recording:', error);
            if (caller) {
                caller.invokeMethodAsync('OnRecordingError', error.message);
            }
        }
    };

    function floatTo16BitPCM(input) {
        const output = new Int16Array(input.length);
        for (let i = 0; i < input.length; i++) {
            const s = Math.max(-1, Math.min(1, input[i]));
            output[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }
        return output;
    }

    function arrayBufferToBase64(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return window.btoa(binary);
    }

    BlazorAudioRecorder.PauseRecord = function () {
        if (processor) {
            processor.disconnect();
        }
        if (mMediaRecorder && mMediaRecorder.state === "recording") {
            mMediaRecorder.pause();
        }
        stopVisualization();
    };

    BlazorAudioRecorder.ResumeRecord = function () {
        if (processor && audioContext && mediaStream) {
            const source = audioContext.createMediaStreamSource(mediaStream);
            source.connect(processor);
            processor.connect(audioContext.destination);
            source.connect(analyser);
            analyser.connect(audioContext.destination);
            startVisualization();
        }
        if (mMediaRecorder && mMediaRecorder.state === "paused") {
            mMediaRecorder.resume();
        }
    };

    BlazorAudioRecorder.CancelRecord = async function () {
        try {
            stopVisualization();

            if (processor) {
                processor.disconnect();
                processor = null;
            }

            if (analyser) {
                analyser.disconnect();
                analyser = null;
            }

            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                mediaStream = null;
            }

            if (audioContext) {
                await audioContext.close();
                audioContext = null;
            }

            if (mMediaRecorder && mMediaRecorder.state !== "inactive") {
                mMediaRecorder.stop();
                mAudioChunks = [];
            }
            return true;
        } catch (error) {
            console.error('Error canceling recording:', error);
            return false;
        }
    };

    BlazorAudioRecorder.StopRecord = async function (currentRecordingId, accessToken) {
        try {
            stopVisualization();

            if (processor) {
                processor.disconnect();
                processor = null;
            }

            if (analyser) {
                analyser.disconnect();
                analyser = null;
            }

            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                mediaStream = null;
            }

            if (mMediaRecorder && mMediaRecorder.state !== "inactive") {
                mMediaRecorder.stop();
                await new Promise(resolve => setTimeout(resolve, 100));


                console.log('Preparing to upload...');
                console.log(currentRecordingId);
                var audioBlob = new Blob(mAudioChunks, { type: "audio/webm;codecs=opus" });

                const formData = new FormData();
                formData.append('audioFile', audioBlob, currentRecordingId);

                const response = await fetch(window.AppSettings.uploadUrl, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${accessToken}`
                    },
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    if (caller) {
                        await caller.invokeMethodAsync('OnRecordingComplete', result.id);
                    }
                }
            }

            if (audioContext) {
                await audioContext.close();
                audioContext = null;
            }

            return true;
        } catch (error) {
            console.error('Error stopping recording:', error);
            return false;
        }
    };
})();

// File download function for DataHub exports
window.downloadFile = function (fileName, base64Content, contentType) {
    try {
        // Convert base64 to blob
        const byteCharacters = atob(base64Content);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: contentType });

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Error downloading file:', error);
    }
};