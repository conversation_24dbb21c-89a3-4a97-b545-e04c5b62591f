﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPatientService
    {
        Patient PatientData { get; set; }
        Task<List<Patient>> SearchPatientsAsync(PatientSearchCriteria criteria);
        Task<Patient> GetPatientByIdAsync(Guid patientId, Guid organizationId);
        Task<List<Patient>> GetPatientsByOrganizationIdAsync(Guid organizationId);
    }
}
