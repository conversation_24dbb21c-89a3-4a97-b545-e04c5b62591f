using System;

namespace TeyaUIModels.Model
{
    // Search Criteria Model for Patient Registry
    public class PatientSearchCriteria
    {
        public int? AgeFrom { get; set; }
        public int? AgeTo { get; set; }
        public string Sex { get; set; } = string.Empty;
        public DateTime? DOBFrom { get; set; }
        public DateTime? DOBTo { get; set; }
        public string PCP { get; set; } = string.Empty;
        public string Insurance { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public Guid OrganizationId { get; set; }

        // Additional search criteria
        public string PatientName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string ZipCode { get; set; } = string.Empty;
        public string EmergencyContact { get; set; } = string.Empty;
        public DateTime? LastVisitFrom { get; set; }
        public DateTime? LastVisitTo { get; set; }
        public string Diagnosis { get; set; } = string.Empty;
        public string Medication { get; set; } = string.Empty;
        public bool? IsActive { get; set; }
        public string RiskLevel { get; set; } = string.Empty;
    }

    // Filter Model for DataHub UI
    public class DataHubFilterModel
    {
        public int? AgeFrom { get; set; }
        public int? AgeTo { get; set; }
        public string Sex { get; set; } = string.Empty;
        public DateTime? DOBFrom { get; set; }
        public DateTime? DOBTo { get; set; }
        public string PCP { get; set; } = string.Empty;
        public string Insurance { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;

        // Additional filter fields
        public string PatientName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string ZipCode { get; set; } = string.Empty;
        public DateTime? LastVisitFrom { get; set; }
        public DateTime? LastVisitTo { get; set; }
        public string Diagnosis { get; set; } = string.Empty;
        public string Medication { get; set; } = string.Empty;
        public bool? IsActive { get; set; }
        public string RiskLevel { get; set; } = string.Empty;
    }

    // Patient Registry Model for DataHub display
    public class PatientRegistryModel
    {
        public Guid Id { get; set; }
        public string FullName { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string Sex { get; set; } = string.Empty;
        public int Age { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;

        // Additional display fields
        public string Address { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string ZipCode { get; set; } = string.Empty;
        public string PCP { get; set; } = string.Empty;
        public string Insurance { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public DateTime? LastVisit { get; set; }
        public string RiskLevel { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public string EmergencyContact { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
    }

    // Saved Query Model for DataHub
    public class SavedQuery
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public Guid UserId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime CreatedDate { get; set; }
        public string FilterCriteria { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsPublic { get; set; } = false;
    }
}
