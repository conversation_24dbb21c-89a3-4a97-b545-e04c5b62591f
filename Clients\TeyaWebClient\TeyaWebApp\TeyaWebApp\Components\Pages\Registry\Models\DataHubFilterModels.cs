using System.ComponentModel.DataAnnotations;

namespace TeyaWebApp.Components.Pages.Registry.Models
{
    // Demographics Tab Filter Model
    public class DemographicsFilterModel
    {
        public int? AgeFrom { get; set; }
        public int? AgeTo { get; set; }
        public string? Sex { get; set; }
        public string? PatientName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? ZipCode { get; set; }
        public DateTime? DOBFrom { get; set; }
        public DateTime? DOBTo { get; set; }
        public string? PCP { get; set; }
        public string? Insurance { get; set; }
        public string? Language { get; set; }
        public string? Race { get; set; }
        public string? Ethnicity { get; set; }
        public string? Facility { get; set; }
        public string? Program { get; set; }
        public string? PatientSearchOption { get; set; } // Active, Inactive, All
    }

    // Vitals Tab Filter Model
    public class VitalsFilterModel
    {
        public decimal? HeightFrom { get; set; }
        public decimal? HeightTo { get; set; }
        public decimal? WeightFrom { get; set; }
        public decimal? WeightTo { get; set; }
        public int? SystolicBPFrom { get; set; }
        public int? SystolicBPTo { get; set; }
        public int? DiastolicBPFrom { get; set; }
        public int? DiastolicBPTo { get; set; }
        public int? HeartRateFrom { get; set; }
        public int? HeartRateTo { get; set; }
        public decimal? TemperatureFrom { get; set; }
        public decimal? TemperatureTo { get; set; }
        public decimal? BMIFrom { get; set; }
        public decimal? BMITo { get; set; }
        public int? RespiratoryRateFrom { get; set; }
        public int? RespiratoryRateTo { get; set; }
        public int? PainScoreFrom { get; set; }
        public int? PainScoreTo { get; set; }
        public decimal? GlucoseFrom { get; set; }
        public decimal? GlucoseTo { get; set; }
        public decimal? PeakFlowFrom { get; set; }
        public decimal? PeakFlowTo { get; set; }
        public DateTime? VitalDateFrom { get; set; }
        public DateTime? VitalDateTo { get; set; }
    }

    // Labs/DI/Procedure Tab Filter Model
    public class LabsFilterModel
    {
        public string? LabName { get; set; }
        public string? LabCode { get; set; }
        public string? ResultValue { get; set; }
        public string? ResultOperator { get; set; } // =, >, <, >=, <=, !=
        public string? Units { get; set; }
        public string? ReferenceRange { get; set; }
        public string? AbnormalFlag { get; set; }
        public bool? FastingRequired { get; set; }
        public DateTime? ResultDateFrom { get; set; }
        public DateTime? ResultDateTo { get; set; }
        public string? OrderingProvider { get; set; }
        public string? LabFacility { get; set; }
        public string? ProcedureType { get; set; } // Lab, Imaging, Procedure
    }

    // ICD Tab Filter Model
    public class ICDFilterModel
    {
        public string? ICDCode { get; set; }
        public string? ICDDescription { get; set; }
        public string? ICDVersion { get; set; } // ICD-9, ICD-10
        public string? ICDGroup { get; set; }
        public string? DiagnosisType { get; set; } // Primary, Secondary, All
        public DateTime? DiagnosisDateFrom { get; set; }
        public DateTime? DiagnosisDateTo { get; set; }
        public string? Provider { get; set; }
        public bool? ActiveProblemsOnly { get; set; }
        public bool? IncludeProblemList { get; set; }
        public bool? IncludeAssessments { get; set; }
    }

    // CPT Tab Filter Model
    public class CPTFilterModel
    {
        public string? CPTCode { get; set; }
        public string? CPTDescription { get; set; }
        public string? CPTGroup { get; set; }
        public string? BillingCategory { get; set; }
        public decimal? FeeFrom { get; set; }
        public decimal? FeeTo { get; set; }
        public DateTime? ServiceDateFrom { get; set; }
        public DateTime? ServiceDateTo { get; set; }
        public string? Provider { get; set; }
        public string? Facility { get; set; }
        public string? InsurancePayer { get; set; }
        public string? ClaimStatus { get; set; }
    }

    // Rx Tab Filter Model
    public class RxFilterModel
    {
        public string? DrugName { get; set; }
        public string? DrugClass { get; set; }
        public string? GenericName { get; set; }
        public string? BrandName { get; set; }
        public string? NDCNumber { get; set; }
        public string? Strength { get; set; }
        public string? DosageForm { get; set; }
        public string? Route { get; set; }
        public DateTime? PrescriptionDateFrom { get; set; }
        public DateTime? PrescriptionDateTo { get; set; }
        public string? PrescriberName { get; set; }
        public string? Pharmacy { get; set; }
        public bool? ActivePrescriptionsOnly { get; set; }
        public string? TherapeuticClass { get; set; }
    }

    // Immunization Tab Filter Model
    public class ImmunizationFilterModel
    {
        public string? VaccineName { get; set; }
        public string? CVXCode { get; set; }
        public string? LotNumber { get; set; }
        public string? Manufacturer { get; set; }
        public DateTime? AdministrationDateFrom { get; set; }
        public DateTime? AdministrationDateTo { get; set; }
        public string? AdministeredBy { get; set; }
        public string? Site { get; set; }
        public string? Route { get; set; }
        public int? ShotCountFrom { get; set; }
        public int? ShotCountTo { get; set; }
        public string? VaccineGroup { get; set; }
        public bool? CompletedSeriesOnly { get; set; }
    }

    // Encounters Tab Filter Model
    public class EncountersFilterModel
    {
        public DateTime? EncounterDateFrom { get; set; }
        public DateTime? EncounterDateTo { get; set; }
        public string? Provider { get; set; }
        public string? Facility { get; set; }
        public string? VisitType { get; set; }
        public string? EncounterType { get; set; }
        public string? ChiefComplaint { get; set; }
        public string? Diagnosis { get; set; }
        public string? Department { get; set; }
        public string? EncounterStatus { get; set; }
        public bool? BilledEncountersOnly { get; set; }
    }

    // Allergies Tab Filter Model
    public class AllergiesFilterModel
    {
        public string? AllergenName { get; set; }
        public string? AllergenType { get; set; } // Drug, Food, Environmental
        public string? Reaction { get; set; }
        public string? Severity { get; set; } // Mild, Moderate, Severe
        public DateTime? OnsetDateFrom { get; set; }
        public DateTime? OnsetDateTo { get; set; }
        public string? Status { get; set; } // Active, Inactive, Resolved
        public bool? StructuredDataOnly { get; set; }
        public bool? IncludeNonStructured { get; set; }
    }

    // Medical History Tab Filter Model
    public class MedicalHistoryFilterModel
    {
        public string? Condition { get; set; }
        public string? ICDCode { get; set; }
        public DateTime? OnsetDateFrom { get; set; }
        public DateTime? OnsetDateTo { get; set; }
        public string? Status { get; set; } // Active, Inactive, Resolved
        public string? Provider { get; set; }
        public bool? ActiveProblemsOnly { get; set; }
        public bool? IncludePastHistory { get; set; }
        public string? FamilyHistory { get; set; }
        public string? SocialHistory { get; set; }
    }

    // Structured Data Tab Filter Model
    public class StructuredDataFilterModel
    {
        public string? FieldName { get; set; }
        public string? FieldValue { get; set; }
        public string? DataType { get; set; }
        public string? Operator { get; set; } // =, >, <, Contains, etc.
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
        public string? Category { get; set; }
        public string? Source { get; set; }
    }

    // Referrals Tab Filter Model
    public class ReferralsFilterModel
    {
        public string? ReferralType { get; set; } // Incoming, Outgoing
        public string? Specialty { get; set; }
        public string? ReferringProvider { get; set; }
        public string? ReferredToProvider { get; set; }
        public DateTime? ReferralDateFrom { get; set; }
        public DateTime? ReferralDateTo { get; set; }
        public string? ICDCode { get; set; }
        public string? ReasonForReferral { get; set; }
        public string? ReferralStatus { get; set; } // Pending, Completed, Cancelled
        public string? Priority { get; set; } // Routine, Urgent, STAT
    }

    // Reports Tab Filter Model
    public class ReportsFilterModel
    {
        public string? ReportType { get; set; }
        public string? ChronicCondition { get; set; }
        public string? QualityMeasure { get; set; }
        public DateTime? ReportDateFrom { get; set; }
        public DateTime? ReportDateTo { get; set; }
        public string? Provider { get; set; }
        public string? Department { get; set; }
        public bool? IncludeFlowsheets { get; set; }
    }

    // Saved Reports Tab Filter Model
    public class SavedReportsFilterModel
    {
        public string? ReportName { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? CreatedDateFrom { get; set; }
        public DateTime? CreatedDateTo { get; set; }
        public string? ReportCategory { get; set; }
        public bool? SharedReportsOnly { get; set; }
        public string? Tags { get; set; }
    }
}
