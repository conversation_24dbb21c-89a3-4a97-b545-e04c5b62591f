namespace TeyaWebApp.Models
{
    /// <summary>
    /// Model for DataHub filter criteria
    /// </summary>
    public class DataHubFilterModel
    {
        public int? AgeFrom { get; set; }
        public int? AgeTo { get; set; }
        public string Sex { get; set; } = string.Empty;
        public string PatientName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Insurance { get; set; } = string.Empty;
        public DateTime? DOBFrom { get; set; }
        public DateTime? DOBTo { get; set; }
        public string PCP { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public bool? IsActive { get; set; }
    }
}
